#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI应用的简单脚本
"""

import os
import sys

def test_imports():
    """测试所有必要的导入"""
    print("🧪 测试导入...")
    
    try:
        import tkinter as tk
        print("✅ tkinter 导入成功")
    except ImportError as e:
        print(f"❌ tkinter 导入失败: {e}")
        return False
    
    try:
        from PIL import Image, ImageTk
        print("✅ PIL 导入成功")
    except ImportError as e:
        print(f"❌ PIL 导入失败: {e}")
        return False
    
    try:
        import cv2
        print("✅ OpenCV 导入成功")
    except ImportError as e:
        print(f"❌ OpenCV 导入失败: {e}")
        return False
    
    try:
        import torch
        print("✅ PyTorch 导入成功")
    except ImportError as e:
        print(f"❌ PyTorch 导入失败: {e}")
        return False
    
    try:
        from ultralytics import YOLO
        print("✅ Ultralytics 导入成功")
    except ImportError as e:
        print(f"❌ Ultralytics 导入失败: {e}")
        return False
    
    return True

def test_model_files():
    """测试模型文件是否存在"""
    print("\n🔍 检查模型文件...")
    
    model_path = 'animal_detection1/yolo11s_exp1/weights/best.pt'
    if os.path.exists(model_path):
        print(f"✅ 训练模型存在: {model_path}")
        return True
    else:
        print(f"❌ 训练模型不存在: {model_path}")
        
        # 检查预训练模型
        pretrained_path = 'yolo11s.pt'
        if os.path.exists(pretrained_path):
            print(f"✅ 预训练模型存在: {pretrained_path}")
            return True
        else:
            print(f"❌ 预训练模型也不存在: {pretrained_path}")
            return False

def test_data_files():
    """测试数据文件是否存在"""
    print("\n📁 检查数据文件...")
    
    data_yaml = 'yqjdataset/data.yaml'
    if os.path.exists(data_yaml):
        print(f"✅ 数据配置文件存在: {data_yaml}")
        return True
    else:
        print(f"❌ 数据配置文件不存在: {data_yaml}")
        return False

def test_train_module():
    """测试train.py模块"""
    print("\n🔧 测试train.py模块...")
    
    try:
        from train import MultiTaskModel, create_integrated_model
        print("✅ train.py 模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ train.py 模块导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试GUI应用环境...")
    
    all_tests_passed = True
    
    # 测试导入
    if not test_imports():
        all_tests_passed = False
    
    # 测试模型文件
    if not test_model_files():
        all_tests_passed = False
    
    # 测试数据文件
    if not test_data_files():
        all_tests_passed = False
    
    # 测试train模块
    if not test_train_module():
        all_tests_passed = False
    
    print("\n" + "="*50)
    if all_tests_passed:
        print("🎉 所有测试通过！可以运行GUI应用")
        print("运行命令: python gui_detection_app.py")
    else:
        print("⚠️  部分测试失败，请检查环境配置")
    
    return all_tests_passed

if __name__ == "__main__":
    main()
