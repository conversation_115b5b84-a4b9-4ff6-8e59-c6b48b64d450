import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from PIL import Image, ImageTk
import cv2
import numpy as np
import os
import torch
import threading
import time
import json
from pathlib import Path

# 导入训练脚本中的模型类
try:
    from train import MultiTaskModel, create_integrated_model
    TRAIN_MODULE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  无法导入训练模块: {e}")
    print("请确保train.py文件在同一目录下")
    TRAIN_MODULE_AVAILABLE = False

class HerbalMedicineDetectionGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🌿 中草药智能识别检测系统 (目标检测+OCR)")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f0f0')

        # 中草药类别中文名称 (从evaluation.py获取)
        self.chinese_names = [
            "白茯苓", "白芍", "白术", "蒲公英", "甘草", "栀子", "党参", "桃仁", "去皮桃仁", "地肤子",
            "牡丹皮", "冬虫夏草", "杜仲", "当归", "杏仁", "何首乌", "黄精", "鸡血藤", "枸杞", "莲须",
            "莲肉", "麦门冬", "木通", "玉竹", "女贞子", "肉苁蓉", "人参", "乌梅", "覆盆子", "瓜蒌皮",
            "肉桂", "山茱萸", "山药", "酸枣仁", "桑白皮", "山楂", "天麻", "熟地黄", "小茴香", "泽泻",
            "竹茹", "川贝母", "川芎", "玄参", "益智仁"
        ]

        # 模型相关变量
        self.model = None
        self.model_loaded = False
        self.current_image = None
        self.current_image_path = None
        self.detection_results = None
        self.prediction_result = None

        # 创建GUI界面
        self.create_widgets()

        # 加载模型
        self.load_model()

    def create_widgets(self):
        """创建GUI组件"""
        # 主标题
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=70)
        title_frame.pack(fill='x', padx=10, pady=(10, 5))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="🌿 中草药智能识别检测系统",
                              font=('Microsoft YaHei', 20, 'bold'),
                              fg='white', bg='#2c3e50')
        title_label.pack(expand=True)

        subtitle_label = tk.Label(title_frame, text="目标检测 + OCR文字识别整合系统",
                                font=('Microsoft YaHei', 12),
                                fg='#ecf0f1', bg='#2c3e50')
        subtitle_label.pack()

        # 主容器
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # 左侧面板 - 控制区域
        left_frame = tk.Frame(main_frame, bg='white', width=350, relief='raised', bd=1)
        left_frame.pack(side='left', fill='y', padx=(0, 5))
        left_frame.pack_propagate(False)

        # 右侧面板 - 图像显示区域
        right_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        right_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))

        # 左侧控制组件
        self.create_control_panel(left_frame)

        # 右侧图像显示组件
        self.create_image_panel(right_frame)

    def create_control_panel(self, parent):
        """创建控制面板"""
        # 模型状态
        status_frame = tk.LabelFrame(parent, text="🤖 模型状态", font=('Microsoft YaHei', 10, 'bold'),
                                   bg='white', fg='#2c3e50')
        status_frame.pack(fill='x', padx=10, pady=10)

        self.status_label = tk.Label(status_frame, text="正在加载整合模型...",
                                   font=('Microsoft YaHei', 9), bg='white', fg='orange')
        self.status_label.pack(pady=5)

        # 文件操作
        file_frame = tk.LabelFrame(parent, text="📁 文件操作", font=('Microsoft YaHei', 10, 'bold'),
                                 bg='white', fg='#2c3e50')
        file_frame.pack(fill='x', padx=10, pady=10)

        self.upload_btn = tk.Button(file_frame, text="📁 选择图片",
                                  command=self.upload_image,
                                  font=('Microsoft YaHei', 10),
                                  bg='#3498db', fg='white',
                                  relief='flat', padx=20, pady=8)
        self.upload_btn.pack(pady=5, fill='x', padx=10)

        self.detect_btn = tk.Button(file_frame, text="🔍 开始检测+OCR",
                                  command=self.start_detection,
                                  font=('Microsoft YaHei', 10),
                                  bg='#27ae60', fg='white',
                                  relief='flat', padx=20, pady=8,
                                  state='disabled')
        self.detect_btn.pack(pady=5, fill='x', padx=10)

        self.save_btn = tk.Button(file_frame, text="💾 保存结果",
                                command=self.save_results,
                                font=('Microsoft YaHei', 10),
                                bg='#e74c3c', fg='white',
                                relief='flat', padx=20, pady=8,
                                state='disabled')
        self.save_btn.pack(pady=5, fill='x', padx=10)

        # 检测参数
        param_frame = tk.LabelFrame(parent, text="⚙️ 检测参数", font=('Microsoft YaHei', 10, 'bold'),
                                  bg='white', fg='#2c3e50')
        param_frame.pack(fill='x', padx=10, pady=10)

        # 目标检测置信度阈值
        tk.Label(param_frame, text="目标检测置信度:", font=('Microsoft YaHei', 9),
                bg='white').pack(anchor='w', padx=10, pady=(5, 0))

        self.conf_var = tk.DoubleVar(value=0.25)
        self.conf_scale = tk.Scale(param_frame, from_=0.1, to=0.9, resolution=0.05,
                                 orient='horizontal', variable=self.conf_var,
                                 font=('Microsoft YaHei', 8), bg='white')
        self.conf_scale.pack(fill='x', padx=10, pady=(0, 5))

        # OCR置信度阈值
        tk.Label(param_frame, text="OCR置信度:", font=('Microsoft YaHei', 9),
                bg='white').pack(anchor='w', padx=10, pady=(5, 0))

        self.ocr_conf_var = tk.DoubleVar(value=0.5)
        self.ocr_conf_scale = tk.Scale(param_frame, from_=0.1, to=0.9, resolution=0.05,
                                     orient='horizontal', variable=self.ocr_conf_var,
                                     font=('Microsoft YaHei', 8), bg='white')
        self.ocr_conf_scale.pack(fill='x', padx=10, pady=(0, 5))