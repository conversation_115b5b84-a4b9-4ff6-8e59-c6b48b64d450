import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import cv2
import numpy as np
import os
import torch
from ultralytics import YOLO
import threading
import time
from pathlib import Path

class HerbalMedicineDetectionGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("中草药识别检测系统")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # 中草药类别中文名称
        self.chinese_names = [
            "白茯苓", "白芍", "白术", "蒲公英", "甘草", "栀子", "党参", "桃仁", "去皮桃仁", "地肤子",
            "牡丹皮", "冬虫夏草", "杜仲", "当归", "杏仁", "何首乌", "黄精", "鸡血藤", "枸杞", "莲须",
            "莲肉", "麦门冬", "木通", "玉竹", "女贞子", "肉苁蓉", "人参", "乌梅", "覆盆子", "瓜蒌皮",
            "肉桂", "山茱萸", "山药", "酸枣仁", "桑白皮", "山楂", "天麻", "熟地黄", "小茴香", "泽泻",
            "竹茹", "川贝母", "川芎", "玄参", "益智仁"
        ]
        
        # 模型相关变量
        self.model = None
        self.model_loaded = False
        self.current_image = None
        self.current_image_path = None
        self.detection_results = None
        
        # 创建GUI界面
        self.create_widgets()
        
        # 加载模型
        self.load_model()
    
    def create_widgets(self):
        """创建GUI组件"""
        # 主标题
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=60)
        title_frame.pack(fill='x', padx=10, pady=(10, 5))
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="🌿 中草药智能识别检测系统", 
                              font=('Microsoft YaHei', 18, 'bold'), 
                              fg='white', bg='#2c3e50')
        title_label.pack(expand=True)
        
        # 主容器
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # 左侧面板 - 控制区域
        left_frame = tk.Frame(main_frame, bg='white', width=300, relief='raised', bd=1)
        left_frame.pack(side='left', fill='y', padx=(0, 5))
        left_frame.pack_propagate(False)
        
        # 右侧面板 - 图像显示区域
        right_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        right_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))
        
        # 左侧控制组件
        self.create_control_panel(left_frame)
        
        # 右侧图像显示组件
        self.create_image_panel(right_frame)
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        # 模型状态
        status_frame = tk.LabelFrame(parent, text="模型状态", font=('Microsoft YaHei', 10, 'bold'), 
                                   bg='white', fg='#2c3e50')
        status_frame.pack(fill='x', padx=10, pady=10)
        
        self.status_label = tk.Label(status_frame, text="正在加载模型...", 
                                   font=('Microsoft YaHei', 9), bg='white', fg='orange')
        self.status_label.pack(pady=5)
        
        # 文件操作
        file_frame = tk.LabelFrame(parent, text="文件操作", font=('Microsoft YaHei', 10, 'bold'), 
                                 bg='white', fg='#2c3e50')
        file_frame.pack(fill='x', padx=10, pady=10)
        
        self.upload_btn = tk.Button(file_frame, text="📁 选择图片", 
                                  command=self.upload_image,
                                  font=('Microsoft YaHei', 10), 
                                  bg='#3498db', fg='white',
                                  relief='flat', padx=20, pady=8)
        self.upload_btn.pack(pady=5, fill='x', padx=10)
        
        self.detect_btn = tk.Button(file_frame, text="🔍 开始检测", 
                                  command=self.start_detection,
                                  font=('Microsoft YaHei', 10), 
                                  bg='#27ae60', fg='white',
                                  relief='flat', padx=20, pady=8,
                                  state='disabled')
        self.detect_btn.pack(pady=5, fill='x', padx=10)
        
        self.save_btn = tk.Button(file_frame, text="💾 保存结果", 
                                command=self.save_results,
                                font=('Microsoft YaHei', 10), 
                                bg='#e74c3c', fg='white',
                                relief='flat', padx=20, pady=8,
                                state='disabled')
        self.save_btn.pack(pady=5, fill='x', padx=10)
        
        # 检测参数
        param_frame = tk.LabelFrame(parent, text="检测参数", font=('Microsoft YaHei', 10, 'bold'), 
                                  bg='white', fg='#2c3e50')
        param_frame.pack(fill='x', padx=10, pady=10)
        
        # 置信度阈值
        tk.Label(param_frame, text="置信度阈值:", font=('Microsoft YaHei', 9), 
                bg='white').pack(anchor='w', padx=10, pady=(5, 0))
        
        self.conf_var = tk.DoubleVar(value=0.25)
        self.conf_scale = tk.Scale(param_frame, from_=0.1, to=0.9, resolution=0.05,
                                 orient='horizontal', variable=self.conf_var,
                                 font=('Microsoft YaHei', 8), bg='white')
        self.conf_scale.pack(fill='x', padx=10, pady=(0, 5))
        
        # 检测结果
        result_frame = tk.LabelFrame(parent, text="检测结果", font=('Microsoft YaHei', 10, 'bold'), 
                                   bg='white', fg='#2c3e50')
        result_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 结果列表
        self.result_tree = ttk.Treeview(result_frame, columns=('class', 'confidence'), 
                                      show='headings', height=8)
        self.result_tree.heading('class', text='识别类别')
        self.result_tree.heading('confidence', text='置信度')
        self.result_tree.column('class', width=150)
        self.result_tree.column('confidence', width=80)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(result_frame, orient='vertical', command=self.result_tree.yview)
        self.result_tree.configure(yscrollcommand=scrollbar.set)
        
        self.result_tree.pack(side='left', fill='both', expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side='right', fill='y', padx=(0, 10), pady=10)
    
    def create_image_panel(self, parent):
        """创建图像显示面板"""
        # 图像显示标题
        img_title = tk.Label(parent, text="图像显示区域", font=('Microsoft YaHei', 12, 'bold'), 
                           bg='white', fg='#2c3e50')
        img_title.pack(pady=10)
        
        # 图像显示画布
        self.canvas_frame = tk.Frame(parent, bg='white')
        self.canvas_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.canvas = tk.Canvas(self.canvas_frame, bg='#ecf0f1', relief='sunken', bd=2)
        self.canvas.pack(fill='both', expand=True)
        
        # 默认提示文本
        self.canvas.create_text(400, 300, text="请选择图片进行检测", 
                              font=('Microsoft YaHei', 16), fill='#7f8c8d')
    
    def load_model(self):
        """加载YOLO模型"""
        def load_in_thread():
            try:
                # 尝试加载训练好的模型
                model_paths = [
                    r'D:\PAchongtext\中医药识别优化系统\animal_detection1\yolo11s_exp1\weights\best.pt',
                    'animal_detection1/yolo11s_exp1/weights/best.pt',
                    'yolo11s.pt'
                ]
                
                model_loaded = False
                for model_path in model_paths:
                    if os.path.exists(model_path):
                        self.model = YOLO(model_path)
                        model_loaded = True
                        self.root.after(0, lambda: self.status_label.config(
                            text=f"✅ 模型加载成功\n{os.path.basename(model_path)}", fg='green'))
                        break
                
                if not model_loaded:
                    self.root.after(0, lambda: self.status_label.config(
                        text="❌ 未找到模型文件", fg='red'))
                    return
                
                self.model_loaded = True
                
            except Exception as e:
                self.root.after(0, lambda: self.status_label.config(
                    text=f"❌ 模型加载失败\n{str(e)}", fg='red'))
        
        # 在后台线程中加载模型
        threading.Thread(target=load_in_thread, daemon=True).start()
    
    def upload_image(self):
        """上传图片"""
        file_types = [
            ('图片文件', '*.jpg *.jpeg *.png *.bmp *.tiff'),
            ('JPEG文件', '*.jpg *.jpeg'),
            ('PNG文件', '*.png'),
            ('所有文件', '*.*')
        ]
        
        file_path = filedialog.askopenfilename(
            title="选择要检测的图片",
            filetypes=file_types
        )
        
        if file_path:
            self.current_image_path = file_path
            self.display_image(file_path)
            
            # 启用检测按钮
            if self.model_loaded:
                self.detect_btn.config(state='normal')
    
    def display_image(self, image_path, results=None):
        """显示图片"""
        try:
            # 读取图片
            image = cv2.imread(image_path)
            if image is None:
                messagebox.showerror("错误", "无法读取图片文件")
                return
            
            # 如果有检测结果，绘制检测框
            if results is not None:
                image = self.draw_detection_results(image, results)
            
            # 转换颜色空间
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 调整图片大小以适应画布
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            
            if canvas_width <= 1 or canvas_height <= 1:
                canvas_width, canvas_height = 800, 600
            
            # 计算缩放比例
            h, w = image_rgb.shape[:2]
            scale = min(canvas_width/w, canvas_height/h, 1.0)
            new_w, new_h = int(w*scale), int(h*scale)
            
            # 调整图片大小
            image_resized = cv2.resize(image_rgb, (new_w, new_h))
            
            # 转换为PIL图片
            pil_image = Image.fromarray(image_resized)
            self.photo = ImageTk.PhotoImage(pil_image)
            
            # 清除画布并显示图片
            self.canvas.delete("all")
            x = (canvas_width - new_w) // 2
            y = (canvas_height - new_h) // 2
            self.canvas.create_image(x, y, anchor='nw', image=self.photo)
            
            self.current_image = image
            
        except Exception as e:
            messagebox.showerror("错误", f"显示图片时出错: {str(e)}")
    
    def draw_detection_results(self, image, results):
        """在图片上绘制检测结果"""
        if len(results) > 0 and len(results[0].boxes) > 0:
            boxes = results[0].boxes
            
            for box in boxes:
                # 获取边界框坐标
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
                confidence = float(box.conf[0])
                class_id = int(box.cls[0])
                
                # 获取类别名称
                if class_id < len(self.chinese_names):
                    class_name = self.chinese_names[class_id]
                else:
                    class_name = f"类别{class_id}"
                
                # 绘制边界框
                cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                
                # 绘制标签
                label = f"{class_name}: {confidence:.2f}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                
                # 绘制标签背景
                cv2.rectangle(image, (x1, y1-label_size[1]-10), 
                            (x1+label_size[0], y1), (0, 255, 0), -1)
                
                # 绘制标签文字
                cv2.putText(image, label, (x1, y1-5), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        
        return image
    
    def start_detection(self):
        """开始检测"""
        if not self.model_loaded:
            messagebox.showerror("错误", "模型未加载")
            return
        
        if not self.current_image_path:
            messagebox.showerror("错误", "请先选择图片")
            return
        
        # 禁用检测按钮，显示进度
        self.detect_btn.config(state='disabled', text="🔄 检测中...")
        
        def detect_in_thread():
            try:
                # 执行检测
                device = '0' if torch.cuda.is_available() else 'cpu'
                results = self.model.predict(
                    self.current_image_path,
                    conf=self.conf_var.get(),
                    device=device,
                    verbose=False
                )
                
                self.detection_results = results
                
                # 在主线程中更新UI
                self.root.after(0, lambda: self.update_detection_results(results))
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"检测失败: {str(e)}"))
                self.root.after(0, lambda: self.detect_btn.config(state='normal', text="🔍 开始检测"))
        
        # 在后台线程中执行检测
        threading.Thread(target=detect_in_thread, daemon=True).start()
    
    def update_detection_results(self, results):
        """更新检测结果显示"""
        try:
            # 清空结果列表
            for item in self.result_tree.get_children():
                self.result_tree.delete(item)
            
            # 添加检测结果
            if len(results) > 0 and len(results[0].boxes) > 0:
                boxes = results[0].boxes
                
                for box in boxes:
                    confidence = float(box.conf[0])
                    class_id = int(box.cls[0])
                    
                    # 获取类别名称
                    if class_id < len(self.chinese_names):
                        class_name = self.chinese_names[class_id]
                    else:
                        class_name = f"类别{class_id}"
                    
                    # 添加到结果列表
                    self.result_tree.insert('', 'end', values=(class_name, f"{confidence:.3f}"))
                
                # 重新显示图片（带检测框）
                self.display_image(self.current_image_path, results)
                
                # 启用保存按钮
                self.save_btn.config(state='normal')
                
                messagebox.showinfo("检测完成", f"检测到 {len(boxes)} 个目标")
            else:
                messagebox.showinfo("检测完成", "未检测到任何目标")
            
        except Exception as e:
            messagebox.showerror("错误", f"更新结果时出错: {str(e)}")
        
        finally:
            # 恢复检测按钮
            self.detect_btn.config(state='normal', text="🔍 开始检测")
    
    def save_results(self):
        """保存检测结果"""
        if self.detection_results is None:
            messagebox.showerror("错误", "没有检测结果可保存")
            return
        
        # 选择保存路径
        save_path = filedialog.asksaveasfilename(
            title="保存检测结果",
            defaultextension=".jpg",
            filetypes=[('JPEG文件', '*.jpg'), ('PNG文件', '*.png'), ('所有文件', '*.*')]
        )
        
        if save_path:
            try:
                # 保存带检测框的图片
                result_image = self.draw_detection_results(
                    cv2.imread(self.current_image_path), 
                    self.detection_results
                )
                cv2.imwrite(save_path, result_image)
                messagebox.showinfo("保存成功", f"结果已保存到: {save_path}")
                
            except Exception as e:
                messagebox.showerror("保存失败", f"保存时出错: {str(e)}")

def main():
    """主函数"""
    root = tk.Tk()
    app = HerbalMedicineDetectionGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
