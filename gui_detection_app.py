import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from PIL import Image, ImageTk
import cv2
import numpy as np
import os
import torch
import threading
import time
import json
from pathlib import Path

# 导入训练脚本中的模型类
try:
    from train import MultiTaskModel, create_integrated_model
    TRAIN_MODULE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  无法导入训练模块: {e}")
    print("请确保train.py文件在同一目录下")
    TRAIN_MODULE_AVAILABLE = False

class ObjectDetectionGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🎯 智能目标检测系统 (YOLO + OCR)")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f0f0')

        # 从data.yaml加载类别信息
        self.load_class_names()

        # 模型相关变量
        self.model = None
        self.model_loaded = False
        self.current_image = None
        self.current_image_path = None
        self.detection_results = None
        self.prediction_result = None

        # 创建GUI界面
        self.create_widgets()

        # 加载模型
        self.load_model()

    def load_class_names(self):
        """从data.yaml加载类别名称"""
        try:
            import yaml
            with open('yqjdataset/data.yaml', 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
                self.num_classes = data.get('nc', 47)
                names = data.get('names', [])

                # 如果names是数字列表，使用注释中的真实名称
                if all(isinstance(name, (int, str)) and str(name).isdigit() for name in names):
                    # 使用注释中的类别名称
                    self.class_names = [
                        "CST_01", "CST_02", "CST_03",
                        "MTF_01", "MTF_02", "MTF_03", "MTF_04", "MTF_05", "MTF_06", "MTF_07", "MTF_08", "MTF_09", "MTF_11",
                        "EXC_01",
                        "CTF_01", "CTF_02", "CTF_03", "CTF_04", "CTF_05", "CTF_06", "CTF_07", "CTF_08", "CTF_09",
                        "REA_01",
                        "CB_K01", "DC_S01", "LD_S01", "QCS_01", "FS_L01", "GR_T01", "CAP_01", "CG_D01", "HC_T01", "HBK_01", "GT_F01",
                        "RC_A01", "DC_G01", "OMA_01", "SED_01", "DCC_01", "FTS_01", "FDS_01", "GND_01", "AMM_01", "VOL_01", "LAR_01",
                        "coil"
                    ]
                else:
                    self.class_names = names

        except Exception as e:
            print(f"无法加载类别名称: {e}")
            # 使用默认的类别名称
            self.num_classes = 47
            self.class_names = [f"类别_{i}" for i in range(47)]

    def create_widgets(self):
        """创建GUI组件"""
        # 主标题
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=70)
        title_frame.pack(fill='x', padx=10, pady=(10, 5))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="� 智能目标检测系统",
                              font=('Microsoft YaHei', 20, 'bold'),
                              fg='white', bg='#2c3e50')
        title_label.pack(expand=True)

        subtitle_label = tk.Label(title_frame, text="YOLO目标检测 + OCR文字识别整合系统",
                                font=('Microsoft YaHei', 12),
                                fg='#ecf0f1', bg='#2c3e50')
        subtitle_label.pack()

        # 主容器
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # 左侧面板 - 控制区域
        left_frame = tk.Frame(main_frame, bg='white', width=350, relief='raised', bd=1)
        left_frame.pack(side='left', fill='y', padx=(0, 5))
        left_frame.pack_propagate(False)

        # 右侧面板 - 图像显示区域
        right_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        right_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))

        # 左侧控制组件
        self.create_control_panel(left_frame)

        # 右侧图像显示组件
        self.create_image_panel(right_frame)

    def create_control_panel(self, parent):
        """创建控制面板"""
        # 模型状态
        status_frame = tk.LabelFrame(parent, text="🤖 模型状态", font=('Microsoft YaHei', 10, 'bold'),
                                   bg='white', fg='#2c3e50')
        status_frame.pack(fill='x', padx=10, pady=10)

        self.status_label = tk.Label(status_frame, text="正在加载整合模型...",
                                   font=('Microsoft YaHei', 9), bg='white', fg='orange')
        self.status_label.pack(pady=5)

        # 文件操作
        file_frame = tk.LabelFrame(parent, text="📁 文件操作", font=('Microsoft YaHei', 10, 'bold'),
                                 bg='white', fg='#2c3e50')
        file_frame.pack(fill='x', padx=10, pady=10)

        self.upload_btn = tk.Button(file_frame, text="📁 选择图片",
                                  command=self.upload_image,
                                  font=('Microsoft YaHei', 10),
                                  bg='#3498db', fg='white',
                                  relief='flat', padx=20, pady=8)
        self.upload_btn.pack(pady=5, fill='x', padx=10)

        self.detect_btn = tk.Button(file_frame, text="🔍 开始检测+OCR",
                                  command=self.start_detection,
                                  font=('Microsoft YaHei', 10),
                                  bg='#27ae60', fg='white',
                                  relief='flat', padx=20, pady=8,
                                  state='disabled')
        self.detect_btn.pack(pady=5, fill='x', padx=10)

        self.save_btn = tk.Button(file_frame, text="💾 保存结果",
                                command=self.save_results,
                                font=('Microsoft YaHei', 10),
                                bg='#e74c3c', fg='white',
                                relief='flat', padx=20, pady=8,
                                state='disabled')
        self.save_btn.pack(pady=5, fill='x', padx=10)

        # 检测参数
        param_frame = tk.LabelFrame(parent, text="⚙️ 检测参数", font=('Microsoft YaHei', 10, 'bold'),
                                  bg='white', fg='#2c3e50')
        param_frame.pack(fill='x', padx=10, pady=10)

        # 目标检测置信度阈值
        tk.Label(param_frame, text="目标检测置信度:", font=('Microsoft YaHei', 9),
                bg='white').pack(anchor='w', padx=10, pady=(5, 0))

        self.conf_var = tk.DoubleVar(value=0.25)
        self.conf_scale = tk.Scale(param_frame, from_=0.1, to=0.9, resolution=0.05,
                                 orient='horizontal', variable=self.conf_var,
                                 font=('Microsoft YaHei', 8), bg='white')
        self.conf_scale.pack(fill='x', padx=10, pady=(0, 5))

        # OCR置信度阈值
        tk.Label(param_frame, text="OCR置信度:", font=('Microsoft YaHei', 9),
                bg='white').pack(anchor='w', padx=10, pady=(5, 0))

        self.ocr_conf_var = tk.DoubleVar(value=0.5)
        self.ocr_conf_scale = tk.Scale(param_frame, from_=0.1, to=0.9, resolution=0.05,
                                     orient='horizontal', variable=self.ocr_conf_var,
                                     font=('Microsoft YaHei', 8), bg='white')
        self.ocr_conf_scale.pack(fill='x', padx=10, pady=(0, 5))

        # 检测结果显示
        result_frame = tk.LabelFrame(parent, text="📊 检测结果", font=('Microsoft YaHei', 10, 'bold'),
                                   bg='white', fg='#2c3e50')
        result_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # 结果文本框
        self.result_text = scrolledtext.ScrolledText(result_frame, height=15, width=40,
                                                   font=('Consolas', 9), bg='#f8f9fa',
                                                   wrap='word')
        self.result_text.pack(fill='both', expand=True, padx=10, pady=10)

        # 初始化结果显示
        self.result_text.insert('1.0', "等待上传图片进行检测...\n\n")
        self.result_text.insert('end', "支持的功能:\n")
        self.result_text.insert('end', "• YOLO目标检测\n")
        self.result_text.insert('end', "• OCR文字识别\n")
        self.result_text.insert('end', "• 结果可视化\n")
        self.result_text.insert('end', "• JSON格式导出\n")
        self.result_text.config(state='disabled')

    def create_image_panel(self, parent):
        """创建图像显示面板"""
        # 图像显示标题
        img_title_frame = tk.Frame(parent, bg='white', height=50)
        img_title_frame.pack(fill='x', padx=10, pady=(10, 5))
        img_title_frame.pack_propagate(False)

        img_title_label = tk.Label(img_title_frame, text="🖼️ 图像显示区域",
                                 font=('Microsoft YaHei', 14, 'bold'),
                                 bg='white', fg='#2c3e50')
        img_title_label.pack(expand=True)

        # 图像显示画布
        self.canvas_frame = tk.Frame(parent, bg='white')
        self.canvas_frame.pack(fill='both', expand=True, padx=10, pady=(0, 10))

        self.canvas = tk.Canvas(self.canvas_frame, bg='#f8f9fa', relief='sunken', bd=2)
        self.canvas.pack(fill='both', expand=True)

        # 滚动条
        v_scrollbar = tk.Scrollbar(self.canvas_frame, orient='vertical', command=self.canvas.yview)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar = tk.Scrollbar(self.canvas_frame, orient='horizontal', command=self.canvas.xview)
        h_scrollbar.pack(side='bottom', fill='x')

        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 初始显示提示
        self.canvas.create_text(400, 300, text="请选择图片开始检测",
                              font=('Microsoft YaHei', 16), fill='#7f8c8d')

    def load_model(self):
        """加载模型"""
        def load_in_thread():
            try:
                if not TRAIN_MODULE_AVAILABLE:
                    raise ImportError("训练模块不可用")

                self.status_label.config(text="正在初始化整合模型...", fg='orange')
                self.root.update()

                # 创建整合模型实例
                self.model = create_integrated_model()

                self.model_loaded = True
                self.status_label.config(text="✅ 整合模型加载成功", fg='green')
                self.upload_btn.config(state='normal')

            except Exception as e:
                self.model_loaded = False
                error_msg = f"❌ 模型加载失败: {str(e)}"
                self.status_label.config(text=error_msg, fg='red')
                print(f"模型加载错误: {e}")

        # 在后台线程中加载模型
        threading.Thread(target=load_in_thread, daemon=True).start()

    def upload_image(self):
        """上传图片"""
        file_types = [
            ('图片文件', '*.jpg *.jpeg *.png *.bmp *.tiff *.webp'),
            ('JPEG文件', '*.jpg *.jpeg'),
            ('PNG文件', '*.png'),
            ('所有文件', '*.*')
        ]

        file_path = filedialog.askopenfilename(
            title="选择要检测的图片",
            filetypes=file_types
        )

        if file_path:
            try:
                # 加载图片
                self.current_image_path = file_path
                image = Image.open(file_path)
                self.current_image = image.copy()

                # 显示图片
                self.display_image(image)

                # 启用检测按钮
                if self.model_loaded:
                    self.detect_btn.config(state='normal')

                # 更新结果显示
                self.update_result_text(f"已加载图片: {os.path.basename(file_path)}\n")
                self.update_result_text(f"图片尺寸: {image.size}\n")
                self.update_result_text("点击'开始检测+OCR'进行分析...\n\n")

            except Exception as e:
                messagebox.showerror("错误", f"无法加载图片: {str(e)}")

    def display_image(self, image, results=None):
        """显示图片和检测结果"""
        try:
            # 计算显示尺寸
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            if canvas_width <= 1 or canvas_height <= 1:
                canvas_width, canvas_height = 800, 600

            # 保持宽高比缩放
            img_width, img_height = image.size
            scale = min(canvas_width / img_width, canvas_height / img_height, 1.0)

            new_width = int(img_width * scale)
            new_height = int(img_height * scale)

            # 如果有检测结果，绘制检测框
            display_image = image.copy()
            if results:
                display_image = self.draw_detection_results(display_image, results)

            # 调整图片大小
            display_image = display_image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # 转换为Tkinter格式
            self.photo = ImageTk.PhotoImage(display_image)

            # 清空画布并显示图片
            self.canvas.delete("all")
            self.canvas.create_image(canvas_width//2, canvas_height//2,
                                   image=self.photo, anchor='center')

            # 更新滚动区域
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))

        except Exception as e:
            print(f"显示图片错误: {e}")

    def draw_detection_results(self, image, results):
        """在图片上绘制检测结果"""
        try:
            import cv2

            # 转换为OpenCV格式
            img_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

            # 绘制目标检测框
            if 'detections' in results:
                for detection in results['detections']:
                    bbox = detection['bbox']
                    class_id = detection['class_id']
                    confidence = detection['confidence']

                    # 获取类别名称
                    class_name = self.class_names[class_id] if class_id < len(self.class_names) else f"类别_{class_id}"

                    # 绘制边界框
                    x1, y1, x2, y2 = map(int, bbox)
                    cv2.rectangle(img_cv, (x1, y1), (x2, y2), (0, 255, 0), 2)

                    # 绘制标签
                    label = f"{class_name}: {confidence:.2f}"
                    label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
                    cv2.rectangle(img_cv, (x1, y1 - label_size[1] - 10),
                                (x1 + label_size[0], y1), (0, 255, 0), -1)
                    cv2.putText(img_cv, label, (x1, y1 - 5),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)

            # 绘制OCR文本框
            if 'ocr_results' in results:
                for ocr_result in results['ocr_results']:
                    if 'bbox' in ocr_result:
                        bbox = ocr_result['bbox']
                        text = ocr_result.get('text', '')
                        confidence = ocr_result.get('confidence', 0)

                        # 绘制文本框
                        x1, y1, x2, y2 = map(int, bbox)
                        cv2.rectangle(img_cv, (x1, y1), (x2, y2), (255, 0, 0), 2)

                        # 绘制文本标签
                        if text:
                            label = f"OCR: {text[:10]}..."
                            cv2.putText(img_cv, label, (x1, y1 - 5),
                                      cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 0), 1)

            # 转换回PIL格式
            result_image = Image.fromarray(cv2.cvtColor(img_cv, cv2.COLOR_BGR2RGB))
            return result_image

        except Exception as e:
            print(f"绘制检测结果错误: {e}")
            return image

    def start_detection(self):
        """开始检测"""
        if not self.current_image or not self.model_loaded:
            messagebox.showwarning("警告", "请先加载图片和模型")
            return

        # 禁用按钮
        self.detect_btn.config(state='disabled')
        self.upload_btn.config(state='disabled')

        # 更新状态
        self.update_result_text("🔍 正在进行目标检测和OCR识别...\n")

        def detect_in_thread():
            try:
                # 保存临时图片
                temp_path = "temp_detection_image.jpg"
                self.current_image.save(temp_path)

                # 进行预测
                self.update_result_text("正在运行整合模型预测...\n")
                results = self.model.predict(
                    image_path=temp_path,
                    save_result=False
                )

                # 保存结果
                self.prediction_result = results

                # 在主线程中更新UI
                self.root.after(0, self.update_detection_results, results)

                # 清理临时文件
                if os.path.exists(temp_path):
                    os.remove(temp_path)

            except Exception as e:
                error_msg = f"检测过程出错: {str(e)}"
                self.root.after(0, self.handle_detection_error, error_msg)

        # 在后台线程中进行检测
        threading.Thread(target=detect_in_thread, daemon=True).start()

    def update_detection_results(self, results):
        """更新检测结果显示"""
        try:
            # 显示带检测结果的图片
            self.display_image(self.current_image, results)

            # 更新结果文本
            self.result_text.config(state='normal')
            self.result_text.delete('1.0', 'end')

            self.result_text.insert('end', "🎯 检测完成！\n\n")

            # 显示目标检测结果
            if 'detections' in results and results['detections']:
                self.result_text.insert('end', "📦 目标检测结果:\n")
                self.result_text.insert('end', "-" * 40 + "\n")

                for i, detection in enumerate(results['detections'], 1):
                    class_id = detection['class_id']
                    confidence = detection['confidence']
                    bbox = detection['bbox']

                    class_name = self.class_names[class_id] if class_id < len(self.class_names) else f"类别_{class_id}"

                    self.result_text.insert('end', f"{i}. {class_name}\n")
                    self.result_text.insert('end', f"   置信度: {confidence:.3f}\n")
                    self.result_text.insert('end', f"   位置: ({bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f})\n\n")
            else:
                self.result_text.insert('end', "📦 目标检测: 未检测到目标\n\n")

            # 显示OCR结果
            if 'ocr_results' in results and results['ocr_results']:
                self.result_text.insert('end', "📝 OCR文字识别结果:\n")
                self.result_text.insert('end', "-" * 40 + "\n")

                for i, ocr_result in enumerate(results['ocr_results'], 1):
                    text = ocr_result.get('text', '')
                    confidence = ocr_result.get('confidence', 0)
                    bbox = ocr_result.get('bbox', [])

                    self.result_text.insert('end', f"{i}. 文字: {text}\n")
                    self.result_text.insert('end', f"   置信度: {confidence:.3f}\n")
                    if bbox:
                        self.result_text.insert('end', f"   位置: ({bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f})\n\n")
            else:
                self.result_text.insert('end', "📝 OCR识别: 未识别到文字\n\n")

            # 显示统计信息
            detection_count = len(results.get('detections', []))
            ocr_count = len(results.get('ocr_results', []))

            self.result_text.insert('end', "📊 统计信息:\n")
            self.result_text.insert('end', "-" * 40 + "\n")
            self.result_text.insert('end', f"检测到目标数量: {detection_count}\n")
            self.result_text.insert('end', f"识别到文字数量: {ocr_count}\n")

            self.result_text.config(state='disabled')

            # 启用按钮
            self.detect_btn.config(state='normal')
            self.upload_btn.config(state='normal')
            self.save_btn.config(state='normal')

        except Exception as e:
            self.handle_detection_error(f"更新结果显示出错: {str(e)}")

    def handle_detection_error(self, error_msg):
        """处理检测错误"""
        self.update_result_text(f"❌ {error_msg}\n")

        # 启用按钮
        self.detect_btn.config(state='normal')
        self.upload_btn.config(state='normal')

        messagebox.showerror("检测错误", error_msg)

    def update_result_text(self, text):
        """更新结果文本"""
        self.result_text.config(state='normal')
        self.result_text.insert('end', text)
        self.result_text.see('end')
        self.result_text.config(state='disabled')
        self.root.update()

    def save_results(self):
        """保存检测结果"""
        if not self.prediction_result:
            messagebox.showwarning("警告", "没有检测结果可保存")
            return

        try:
            # 选择保存位置
            file_path = filedialog.asksaveasfilename(
                title="保存检测结果",
                defaultextension=".json",
                filetypes=[
                    ('JSON文件', '*.json'),
                    ('文本文件', '*.txt'),
                    ('所有文件', '*.*')
                ]
            )

            if file_path:
                if file_path.endswith('.json'):
                    # 保存为JSON格式
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(self.prediction_result, f, ensure_ascii=False, indent=2)
                else:
                    # 保存为文本格式
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write("智能目标检测系统 - 检测结果\n")
                        f.write("=" * 50 + "\n\n")

                        # 写入目标检测结果
                        if 'detections' in self.prediction_result:
                            f.write("目标检测结果:\n")
                            f.write("-" * 30 + "\n")
                            for i, detection in enumerate(self.prediction_result['detections'], 1):
                                class_id = detection['class_id']
                                confidence = detection['confidence']
                                bbox = detection['bbox']
                                class_name = self.class_names[class_id] if class_id < len(self.class_names) else f"类别_{class_id}"

                                f.write(f"{i}. {class_name}\n")
                                f.write(f"   置信度: {confidence:.3f}\n")
                                f.write(f"   位置: ({bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f})\n\n")

                        # 写入OCR结果
                        if 'ocr_results' in self.prediction_result:
                            f.write("\nOCR文字识别结果:\n")
                            f.write("-" * 30 + "\n")
                            for i, ocr_result in enumerate(self.prediction_result['ocr_results'], 1):
                                text = ocr_result.get('text', '')
                                confidence = ocr_result.get('confidence', 0)
                                bbox = ocr_result.get('bbox', [])

                                f.write(f"{i}. 文字: {text}\n")
                                f.write(f"   置信度: {confidence:.3f}\n")
                                if bbox:
                                    f.write(f"   位置: ({bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f})\n\n")

                messagebox.showinfo("成功", f"检测结果已保存到: {file_path}")

        except Exception as e:
            messagebox.showerror("错误", f"保存结果失败: {str(e)}")


def main():
    """主函数"""
    root = tk.Tk()
    ObjectDetectionGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()